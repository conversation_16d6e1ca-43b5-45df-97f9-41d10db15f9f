const fs = require('fs');
const pako = require('pako');
const graphology = require('graphology');
const layouts = require('graphology-layout');
const forceAtlas2 = require('graphology-layout-forceatlas2');

// Color utility function (same as in utils.ts)
function lightenColor(color, amount) {
  const usePound = color[0] === "#";
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? "#" : "") + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
}

function readPakoJSON(filename) {
  console.log("Reading " + filename + " ...");
  const pakofile = fs.readFileSync(filename, {flag:'r'});
  return graphology.Graph.from(JSON.parse(pako.inflate(pakofile, {to: "string"})));
}

function writePakoJSON(graph, filename) {
  console.log("Writing " + filename + " ...");
  fs.writeFileSync(filename, pako.deflate(JSON.stringify(graph)));
}

function spatializeHPChapter(filename) {
  const graph = readPakoJSON(filename);
  
  console.log(`Spatializing ${filename} with ${graph.order} nodes and ${graph.size} edges...`);
  
  // Set initial circular positions
  const circularPositions = layouts.circular(graph, { scale: 50 });
  
  graph.forEachNode(node => {
    const importance = graph.getNodeAttribute(node, 'importance_score') || 1;
    const size = Math.pow(importance, 0.3) * 2.5; // Adjusted for HP character count

    graph.mergeNodeAttributes(node, {
      x: circularPositions[node].x,
      y: circularPositions[node].y,
      size: size
      // Remove pre-set visual attributes - let applyEnhancedStyling handle them consistently
    });
  });
  
  // Apply ForceAtlas2 layout for better positioning
  const settings = {
    iterations: 100,
    settings: {
      gravity: 0.5,
      scalingRatio: 10,
      strongGravityMode: true,
      barnesHutOptimize: false
    }
  };

  forceAtlas2.assign(graph, settings);

  // Set colors based on communities (same as global data processing)
  const communities = {};
  const colors = [
    '#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6',
    '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
  ];

  graph.forEachNode((node, attrs) => {
    const community = attrs.community || 0;
    if (!communities[community]) {
      communities[community] = colors[Object.keys(communities).length % colors.length];
    }
    const nodeColor = communities[community];
    graph.setNodeAttribute(node, 'color', nodeColor);

    // Remove halo color setting - let applyEnhancedStyling handle it consistently
  });
  
  // Scale positions to fit better in viewport
  const positions = {};
  graph.forEachNode(node => {
    positions[node] = graph.getNodeAttributes(node);
  });
  
  // Find bounds
  let minX = Infinity, maxX = -Infinity, minY = Infinity, maxY = -Infinity;
  Object.values(positions).forEach(pos => {
    minX = Math.min(minX, pos.x);
    maxX = Math.max(maxX, pos.x);
    minY = Math.min(minY, pos.y);
    maxY = Math.max(maxY, pos.y);
  });
  
  // Scale to fit in a reasonable range
  const scaleX = 100 / (maxX - minX);
  const scaleY = 100 / (maxY - minY);
  const scale = Math.min(scaleX, scaleY) * 0.8; // Leave some margin
  
  graph.forEachNode(node => {
    const pos = positions[node];
    graph.mergeNodeAttributes(node, {
      x: (pos.x - (minX + maxX) / 2) * scale,
      y: (pos.y - (minY + maxY) / 2) * scale
    });
  });
  
  writePakoJSON(graph, filename);
  return graph;
}

// Process all HP chapter files
for (let i = 1; i <= 8; i++) {
  const filename = `data/HarryPotter_characters_by_stories_c${i}.json.gz`;
  if (fs.existsSync(filename)) {
    spatializeHPChapter(filename);
  } else {
    console.log(`Warning: ${filename} not found, skipping...`);
  }
}

console.log("All HP chapter networks spatialized!");
