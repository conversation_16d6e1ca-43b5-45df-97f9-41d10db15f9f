#!/usr/bin/env python3
"""
Process Harry <PERSON> chapter JSON files and create compressed network data
for the Marvel-style graph visualization.
"""

import json
import gzip
import os
import sys
from collections import defaultdict
import networkx as nx

def load_chapter_data(chapter_file):
    """Load and parse a single chapter JSON file."""
    with open(chapter_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_chapter_network(chapter_data):
    """Create a network graph from chapter data."""
    G = nx.Graph()
    
    # Add characters as nodes
    characters = {}
    for char in chapter_data['characters']:
        char_id = char['character_id']
        characters[char_id] = char
        
        G.add_node(char_id, **{
            'label': char['character_name'],
            'name': char['character_name'],
            'importance_score': char['importance_score'],
            'presence_level': char['presence_level'],
            'emotional_state': char['emotional_state'],
            'actions': char['actions'],
            'character_goals': char.get('character_goals', []),
            'image': 'placeholder.jpg',
            'image_url': 'images/characters/default_character.svg',
            'description': f"Emotional state: {char['emotional_state']}. Actions: {', '.join(char['actions'][:3])}...",
            'community': hash(char['character_name']) % 8,  # Simple community assignment
            'url': f'#character/{char_id}',
            'x': 0,
            'y': 0,
            'size': char['importance_score'],
            'color': '#666666'
        })
    
    # Add relationships as edges
    for rel in chapter_data['relationships']:
        char_a = rel['character_a_id']
        char_b = rel['character_b_id']
        
        if char_a in characters and char_b in characters:
            G.add_edge(char_a, char_b, **{
                'relationship_type': rel['relationship_type'],
                'interaction_summary': rel['interaction_summary'],
                'strength_score': rel['strength_score'],
                'emotional_intensity': rel['emotional_intensity'],
                'dialogue_exchanges': rel['dialogue_exchanges'],
                'relationship_change': rel.get('relationship_change', 0),
                'weight': rel['strength_score'],
                'size': max(1, rel['strength_score'] / 2),
                'color': '#ffffff'
            })
    
    return G

def convert_to_sigma_format(G, chapter_num):
    """Convert NetworkX graph to Sigma.js format."""
    # Convert to the format expected by the Marvel visualization
    nodes = []
    edges = []
    
    for node_id, attrs in G.nodes(data=True):
        nodes.append({
            'key': node_id,
            'attributes': attrs
        })
    
    for source, target, attrs in G.edges(data=True):
        edges.append({
            'key': f"{source}_{target}",
            'source': source,
            'target': target,
            'attributes': attrs
        })
    
    return {
        'attributes': {},
        'options': {
            'allowSelfLoops': False,
            'multi': False,
            'type': 'undirected'
        },
        'nodes': nodes,
        'edges': edges
    }

def main():
    hp_data_dir = 'hp_data'
    output_dir = 'data'
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each chapter file
    for i in range(1, 9):  # c1.json through c8.json
        chapter_file = os.path.join(hp_data_dir, f'c{i}.json')
        
        if not os.path.exists(chapter_file):
            print(f"Warning: {chapter_file} not found, skipping...")
            continue
            
        print(f"Processing {chapter_file}...")
        
        # Load chapter data
        chapter_data = load_chapter_data(chapter_file)
        
        # Create network
        G = create_chapter_network(chapter_data)
        
        # Convert to Sigma format
        sigma_data = convert_to_sigma_format(G, i)
        
        # Save as compressed JSON
        output_file = os.path.join(output_dir, f'HarryPotter_characters_by_stories_c{i}.json.gz')
        with gzip.open(output_file, 'wt', encoding='utf-8') as f:
            json.dump(sigma_data, f, separators=(',', ':'))
        
        print(f"Created {output_file} with {len(sigma_data['nodes'])} nodes and {len(sigma_data['edges'])} edges")

if __name__ == '__main__':
    main()
